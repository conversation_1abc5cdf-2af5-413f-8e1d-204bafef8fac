# Frontend Component Refactoring Plan

## 1. Introduction

This document outlines a plan for refactoring the frontend codebase by identifying and extracting reusable UI patterns into dedicated Vue components. The primary goal is to improve code maintainability, reduce duplication, ensure UI consistency, and accelerate future development.

Based on an analysis of the existing views and components, several patterns have been identified as prime candidates for abstraction.

## 2. Proposed Reusable Components

The following components are proposed for creation under `frontend/src/components/`:

1.  **`BaseInput.vue`**: A versatile input component with support for leading/trailing icons and validation messages.
2.  **`Avatar.vue`**: A component to display user avatars, with fallback to initials and an online status indicator.
3.  **`PageHeader.vue`**: A standardized page header with a title and a slot for action buttons.
4.  **`SearchBar.vue`**: A dedicated search input component.
5.  **`ListItem.vue`**: A flexible list item component for displaying contacts, chat threads, etc.
6.  **`EmptyState.vue`**: A component to display a message when a list or content area is empty.
7.  **`ActionButton.vue`**: A standardized icon button for various actions.
8.  **`MessageBubble.vue`**: A component for displaying individual chat messages.

---

## 3. Detailed Component Specifications

### 3.1. `BaseInput.vue`

-   **Description**: A foundational input component to replace all standard text inputs.
-   **Props**:
    -   `modelValue` (for `v-model`)
    -   `type: String` (e.g., 'text', 'password')
    -   `placeholder: String`
    -   `label: String` (optional)
    -   `errorMessage: String` (optional)
-   **Slots**:
    -   `#leading`: For an icon or element at the start of the input.
    -   `#trailing`: For an icon or button at the end of the input (e.g., password visibility toggle).

-   **Example (`Login.vue` - Password Input)**

    **Before:**
    ```html
    <div class="input-container">
      <Lock class="input-icon" />
      <input 
        :type="showPassword ? 'text' : 'password'" 
        class="form-input" 
        placeholder="请输入密码"
        v-model="loginForm.password"
      />
      <button type="button" @click="showPassword = !showPassword">
        <Eye v-if="showPassword" />
        <EyeOff v-else />
      </button>
    </div>
    ```

    **After:**
    ```html
    <BaseInput v-model="loginForm.password" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码">
      <template #leading>
        <Lock />
      </template>
      <template #trailing>
        <button type="button" @click="showPassword = !showPassword">
          <Eye v-if="showPassword" />
          <EyeOff v-else />
        </button>
      </template>
    </BaseInput>
    ```

---

### 3.2. `Avatar.vue`

-   **Description**: Encapsulates all logic for displaying user avatars.
-   **Props**:
    -   `src: String` (Image URL)
    -   `name: String` (For generating initials as a fallback)
    -   `isOnline: Boolean` (Displays a green dot if true)
    -   `size: String` (e.g., 'sm', 'md', 'lg')

-   **Example (`ContactList.vue`)**

    **Before:**
    ```html
    <div class="avatar">
      <img :src="contact.avatar" :alt="contact.name" @error="handleImageError" v-show="!contact.showInitials" />
      <div v-show="contact.showInitials" class="avatar-initials" :style="{ backgroundColor: getAvatarColor(contact.name) }">
        {{ getInitials(contact.name) }}
      </div>
      <div v-if="contact.isOnline" class="online-indicator"></div>
    </div>
    ```

    **After:**
    ```html
    <Avatar :src="contact.avatar" :name="contact.name" :is-online="contact.isOnline" size="lg" />
    ```

---

### 3.3. `PageHeader.vue`

-   **Description**: A standardized header for main views.
-   **Props**:
    -   `title: String`
-   **Slots**:
    -   `#actions`: For placing action buttons on the right side.

-   **Example (`ContactList.vue`)**

    **Before:**
    ```html
    <header class="app-bar">
      <div class="header-content">
        <h1>Contacts</h1>
        <div class="header-actions">
          <button class="action-btn">
            <UserPlus />
          </button>
        </div>
      </div>
    </header>
    ```

    **After:**
    ```html
    <PageHeader title="Contacts">
      <template #actions>
        <ActionButton>
          <UserPlus />
        </ActionButton>
      </template>
    </PageHeader>
    ```

---

### 3.4. `EmptyState.vue`

-   **Description**: A reusable component for displaying empty state messages.
-   **Props**:
    -   `title: String`
    -   `subtitle: String`
-   **Slots**:
    -   `#icon`: For the main icon of the empty state.

-   **Example (`ContactList.vue`)**

    **Before:**
    ```html
    <div class="empty-state">
      <div class="empty-icon">
        <Users color="#6c757d" />
      </div>
      <h3 class="empty-title">No contacts yet</h3>
      <p class="empty-text">Add contacts to start chatting</p>
    </div>
    ```

    **After:**
    ```html
    <EmptyState title="No contacts yet" subtitle="Add contacts to start chatting">
      <template #icon>
        <Users color="#6c757d" />
      </template>
    </EmptyState>
    ```

---

## 4. Benefits of Refactoring

-   **Reduced Code Duplication**: Eliminates repeated code for common UI elements like headers, avatars, and inputs.
-   **Improved Maintainability**: Changes to a common component (e.g., updating the style of all input fields) only need to be made in one place.
-   **UI/UX Consistency**: Ensures that elements like buttons, headers, and empty states look and behave consistently across the entire application.
-   **Faster Development**: Developers can assemble new views more quickly by reusing these foundational components instead of rebuilding them from scratch.
-   **Clearer Code**: Component templates become more semantic and easier to read, as complex UI logic is abstracted away into dedicated child components.
