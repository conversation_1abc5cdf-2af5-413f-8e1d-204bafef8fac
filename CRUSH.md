# Crush
This file contains instructions for an agentic coding assistant to modify this codebase.

## Project Structure
- **/backend**: Python FastAPI backend
- **/frontend**: Vue 3 + TypeScript frontend

## Backend (Python)
- **Dependency Management**: Use `uv` to manage dependencies. Add new dependencies to `pyproject.toml`.
- **Linting**: `black`, `isort`, `flake8`, and `mypy` are used. Run `black . && isort . && flake8 . && mypy .` to lint.
- **Testing**: `pytest` is used for testing.
  - Run all tests: `pytest`
  - Run a specific test file: `pytest tests/test_specific_file.py`
  - Run a specific test function: `pytest tests/test_specific_file.py::test_function_name`
- **Code Style**:
  - Follow `black` and `isort` for formatting.
  - Use type hints for all function signatures.
  - Use f-strings for string formatting.
  - Use underscore-separated names for variables and functions (snake_case).
  - Raise exceptions for errors and handle them in the API layer.

## Frontend (TypeScript/Vue)
- **Dependency Management**: Use `bun` for package management. Add new dependencies with `bun add`.
- **Build**: `bun run build`
- **Development Server**: `bun run dev`
- **Linting**: `eslint` and `prettier` are used. No specific lint command is defined, but it's configured.
- **Code Style**:
  - Follow `prettier` for formatting.
  - Use kebab-case for component file names (e.g., `MyComponent.vue`).
  - Use PascalCase for component names in scripts.
  - Use TypeScript for all new code.
- **State Management**: Pinia is used for state management.
