<template>
  <div class="register">
    <!-- 顶部导航栏 -->
    <header class="app-bar">
      <div class="header-content">
        <button class="back-btn" @click="$router.back()">
          <ChevronLeft :size="24" />
        </button>
        <h1>注册</h1>
        <div class="header-spacer"></div>
      </div>
    </header>

    <!-- 注册内容区域 -->
    <div class="register-content">
      <!-- 欢迎信息 -->
      <div class="welcome-section">
        <div class="app-logo">
          <div class="logo-icon">
            <UserPlus :size="48" />
          </div>
        </div>
        <h2 class="welcome-title">创建账户</h2>
        <p class="welcome-subtitle">加入我们，开始精彩的聊天体验</p>
      </div>

      <!-- 注册表单 -->
      <div class="form-section">
        <form @submit.prevent="handleRegister" class="register-form">
          <div class="form-group">
            <div class="input-container">
              <User class="input-icon" />
              <input 
                type="text" 
                class="form-input" 
                placeholder="请输入用户名"
                v-model="registerForm.username"
              />
            </div>
          </div>
          
          <div class="form-group">
            <div class="input-container">
              <AtSign class="input-icon" />
              <input 
                type="email" 
                class="form-input" 
                placeholder="请输入邮箱地址"
                v-model="registerForm.email"
              />
            </div>
          </div>
          
          <div class="form-group">
            <div class="input-container">
              <Smile class="input-icon" />
              <input 
                type="text" 
                class="form-input" 
                placeholder="请输入昵称"
                v-model="registerForm.nickname"
              />
            </div>
          </div>
          
          <div class="form-group">
            <div class="input-container">
              <Lock class="input-icon" />
              <input 
                :type="showPassword ? 'text' : 'password'" 
                class="form-input" 
                placeholder="请输入密码"
                v-model="registerForm.password"
              />
              <button 
                type="button" 
                class="password-toggle"
                @click="showPassword = !showPassword"
              >
                <Eye v-if="showPassword" />
                <EyeOff v-else />
              </button>
            </div>
          </div>
          
          <div class="form-group">
            <div class="input-container">
              <Shield class="input-icon" />
              <input 
                :type="showConfirmPassword ? 'text' : 'password'" 
                class="form-input" 
                placeholder="请确认密码"
                v-model="registerForm.confirmPassword"
              />
              <button 
                type="button" 
                class="password-toggle"
                @click="showConfirmPassword = !showConfirmPassword"
              >
                <Eye v-if="showConfirmPassword" />
                <EyeOff v-else />
              </button>
            </div>
            <div v-if="passwordError" class="error-message">
              {{ passwordError }}
            </div>
          </div>

          <div class="form-options">
            <label class="agreement">
              <input type="checkbox" v-model="registerForm.agreeTerms" required />
              <span class="checkmark"></span>
              <span class="label-text">
                我已阅读并同意
                <a href="#" class="terms-link">用户协议</a>
                和
                <a href="#" class="terms-link">隐私政策</a>
              </span>
            </label>
          </div>

          <button type="submit" class="register-btn" :disabled="isLoading || !isFormValid">
            <span v-if="!isLoading">注册</span>
            <span v-else class="loading-text">
              <div class="loading-spinner"></div>
              注册中...
            </span>
          </button>
        </form>

        <!-- 登录链接 -->
        <div class="login-section">
          <p class="login-text">
            已有账户？
            <router-link to="/login" class="login-link">
              立即登录
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ChevronLeft, UserPlus, User, AtSign, Smile, Lock, Shield, Eye, EyeOff } from 'lucide-vue-next'
import { showSuccess, showError, showWarning, isValidEmail } from '@/utils'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const registerForm = ref({
  username: '',
  email: '',
  nickname: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 状态管理
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isLoading = ref(false)

// 密码验证
const passwordError = computed(() => {
  if (!registerForm.value.password || !registerForm.value.confirmPassword) {
    return ''
  }
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    return '两次输入的密码不一致'
  }
  if (registerForm.value.password.length < 6) {
    return '密码长度至少6位'
  }
  return ''
})

// 表单验证
const isFormValid = computed(() => {
  return registerForm.value.username &&
         registerForm.value.email &&
         registerForm.value.nickname &&
         registerForm.value.password &&
         registerForm.value.confirmPassword &&
         registerForm.value.agreeTerms &&
         !passwordError.value
})

// 注册处理
const handleRegister = async () => {
  // 详细表单验证
  if (!registerForm.value.username.trim()) {
    showWarning('请输入用户名')
    return
  }

  if (registerForm.value.username.length < 3) {
    showWarning('用户名长度至少3位')
    return
  }

  if (!registerForm.value.email.trim()) {
    showWarning('请输入邮箱地址')
    return
  }

  if (!isValidEmail(registerForm.value.email)) {
    showWarning('请输入有效的邮箱地址')
    return
  }

  if (!registerForm.value.nickname.trim()) {
    showWarning('请输入昵称')
    return
  }

  if (!registerForm.value.password.trim()) {
    showWarning('请输入密码')
    return
  }

  if (passwordError.value) {
    showWarning(passwordError.value)
    return
  }

  if (!registerForm.value.agreeTerms) {
    showWarning('请同意用户协议和隐私政策')
    return
  }

  isLoading.value = true

  try {
    // 调用注册API
    const result = await userStore.register({
      username: registerForm.value.username.trim(),
      email: registerForm.value.email.trim(),
      nickname: registerForm.value.nickname.trim(),
      password: registerForm.value.password
    })

    if (result.success) {
      // 注册成功后跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  } catch (error) {
    console.error('注册失败:', error)
    // 错误已经在store中处理并显示了
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.register {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

.app-bar {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--space-3) var(--space-4);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  box-shadow: var(--shadow-xs);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-btn {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: var(--color-background-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

.back-btn:hover {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.app-bar h1 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  letter-spacing: var(--letter-spacing-tight);
}

.header-spacer {
  width: var(--button-height-sm);
}

.register-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--space-6) var(--space-4);
  overflow-y: auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: var(--space-8);
}

.app-logo {
  margin-bottom: var(--space-6);
}

.logo-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--space-20);
  height: var(--space-20);
  background: var(--color-success);
  border-radius: var(--radius-2xl);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-lg);
}

.welcome-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-2) 0;
  letter-spacing: var(--letter-spacing-tight);
}

.welcome-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.form-section {
  flex: 1;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.register-form {
  margin-bottom: var(--space-8);
}

.form-group {
  margin-bottom: var(--space-5);
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--space-4);
  color: var(--color-text-tertiary);
  z-index: 1;
}

.form-input {
  width: 100%;
  height: var(--space-12);
  padding: 0 var(--space-4) 0 var(--space-12);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-lg);
  background: var(--color-background-secondary);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  outline: none;
  transition: all var(--duration-fast) var(--ease-out);
}

.form-input::placeholder {
  color: var(--color-text-tertiary);
}

.form-input:focus {
  border-color: var(--color-border-focus);
  background: var(--color-background-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.password-toggle {
  position: absolute;
  right: var(--space-4);
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-out);
}

.password-toggle:hover {
  color: var(--color-text-secondary);
  background: var(--color-background-tertiary);
}

.error-message {
  margin-top: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--color-error);
  padding-left: var(--space-12);
}

.form-options {
  margin-bottom: var(--space-6);
}

.agreement {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  user-select: none;
  line-height: var(--line-height-normal);
}

.agreement input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: var(--space-5);
  height: var(--space-5);
  border: 2px solid var(--color-border-secondary);
  border-radius: var(--radius-sm);
  margin-right: var(--space-2);
  margin-top: var(--space-1);
  position: relative;
  flex-shrink: 0;
  transition: all var(--duration-fast) var(--ease-out);
}

.agreement input[type="checkbox"]:checked + .checkmark {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.agreement input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.label-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.terms-link {
  color: var(--color-text-link);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-out);
}

.terms-link:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}

.register-btn {
  width: 100%;
  height: var(--space-12);
  background: var(--color-success);
  color: var(--color-text-inverse);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.register-btn:hover:not(:disabled) {
  background: var(--color-success-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.register-btn:active:not(:disabled) {
  transform: translateY(0);
}

.register-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.loading-spinner {
  width: var(--space-4);
  height: var(--space-4);
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.login-section {
  text-align: center;
}

.login-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

.login-link {
  color: var(--color-text-link);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color var(--duration-fast) var(--ease-out);
}

.login-link:hover {
  color: var(--color-primary-600);
}

/* 图标样式 */
.input-icon {
  width: 20px;
  height: 20px;
}

.password-toggle svg {
  width: 20px;
  height: 20px;
  transition: all 0.2s ease-in-out;
}

.password-toggle:hover svg {
  width: 22px;
  height: 22px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .register-content {
    padding: var(--space-4) var(--space-3);
  }
  
  .welcome-title {
    font-size: var(--font-size-2xl);
  }
  
  .welcome-subtitle {
    font-size: var(--font-size-base);
  }
  
  .label-text {
    font-size: var(--font-size-xs);
  }
}
</style>