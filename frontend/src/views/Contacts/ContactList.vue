<template>
  <div class="contact-list">
    <header class="app-bar">
      <div class="header-content">
        <h1>Contacts</h1>
        <div class="header-actions">
          <button class="action-btn">
            <UserPlus />
          </button>
        </div>
      </div>
    </header>
    
    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-container">
        <Search class="search-icon" />
        <input 
          type="text" 
          class="search-input" 
          placeholder="Search contacts..."
          v-model="searchQuery"
          @input="handleSearch"
        />
      </div>
    </div>
    
    <!-- 联系人列表 -->
    <div class="contact-content">
      <div v-if="filteredContactList.length === 0" class="empty-state">
        <div class="empty-icon">
          <Users color="#6c757d" />
        </div>
        <h3 class="empty-title">No contacts yet</h3>
        <p class="empty-text">Add contacts to start chatting</p>
      </div>
      
      <div v-else class="contact-items">
        <div v-for="contact in filteredContactList" :key="contact.id" class="contact-item" @click="startChat(contact)">
          <div class="avatar">
            <img 
              :src="contact.avatar" 
              :alt="contact.name" 
              @error="handleImageError"
              v-show="!contact.showInitials"
            />
            <div 
              v-show="contact.showInitials" 
              class="avatar-initials"
              :style="{ backgroundColor: getAvatarColor(contact.name) }"
            >
              {{ getInitials(contact.name) }}
            </div>
            <div v-if="contact.isOnline" class="online-indicator"></div>
          </div>
          <div class="contact-info">
            <h3 class="contact-name">{{ contact.name }}</h3>
            <p class="contact-status">{{ contact.status }}</p>
          </div>
          <div class="contact-actions">
            <button class="action-btn small" @click.stop="callContact(contact)">
              <Phone />
            </button>
            <button class="action-btn small" @click.stop="videoCall(contact)">
              <Video />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Search, UserPlus, Users, Phone, Video } from 'lucide-vue-next'

// 定义组件名称，用于 KeepAlive
defineOptions({
  name: 'ContactList'
})

const router = useRouter()

// 搜索相关
const searchQuery = ref('')

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

// 过滤后的联系人列表
const filteredContactList = computed(() => {
  if (!searchQuery.value.trim()) {
    return contactList.value
  }
  return contactList.value.filter(contact => 
    contact.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    contact.status.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 联系人列表数据
const contactList = ref([
  {
    id: 1,
    name: 'Alice Johnson',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    status: 'Available',
    isOnline: true,
    showInitials: false
  },
  {
    id: 2,
    name: 'Bob Smith',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    status: 'Busy',
    isOnline: false,
    showInitials: false
  },
  {
    id: 3,
    name: 'Carol Davis',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    status: 'Away',
    isOnline: true,
    showInitials: false
  },
  {
    id: 4,
    name: 'David Wilson',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    status: 'Available',
    isOnline: true,
    showInitials: false
  },
  {
    id: 5,
    name: 'Emma Brown',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    status: 'Do not disturb',
    isOnline: false,
    showInitials: false
  }
])

// 开始聊天
const startChat = (contact: any) => {
  router.push(`/chat/${contact.id}`)
}

// 语音通话
const callContact = (contact: any) => {
  console.log('Calling:', contact.name)
  // 这里可以集成语音通话功能
}

// 视频通话
const videoCall = (contact: any) => {
  console.log('Video calling:', contact.name)
  // 这里可以集成视频通话功能
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  const contactItem = contactList.value.find(contact => contact.avatar === target.src)
  if (contactItem) {
    contactItem.showInitials = true
  }
}

// 获取用户名首字母
const getInitials = (name: string) => {
  return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2)
}

// 根据用户名生成头像颜色
const getAvatarColor = (name: string) => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  return colors[Math.abs(hash) % colors.length]
}
</script>

<style scoped>
.contact-list {
  height: 100%;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
}

.app-bar {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--space-3) var(--space-4);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  box-shadow: var(--shadow-xs);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-bar h1 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  letter-spacing: var(--letter-spacing-tight);
}

.header-actions {
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: var(--color-background-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

.action-btn:hover {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.search-section {
  padding: var(--space-3) var(--space-4);
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  color: var(--color-text-tertiary);
  z-index: 1;
}

.search-input {
  width: 100%;
  height: var(--input-height-sm);
  padding: 0 var(--space-3) 0 var(--space-10);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-full);
  background: var(--color-background-secondary);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  outline: none;
  transition: all var(--duration-fast) var(--ease-out);
}

.search-input::placeholder {
  color: var(--color-text-tertiary);
}

.search-input:focus {
  border-color: var(--color-border-focus);
  background: var(--color-background-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.action-btn.small {
  width: var(--space-7);
  height: var(--space-7);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-xs);
}

.contact-content {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--space-10) var(--space-5);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--space-6);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-2) 0;
}

.empty-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.contact-items {
  padding: var(--space-2) 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  border-bottom: 1px solid var(--color-border-primary);
  margin: 0 var(--space-3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-1);
}

.contact-item:hover {
  background: var(--color-background-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.contact-item:last-child {
  border-bottom: none;
}

.avatar {
  position: relative;
  margin-right: var(--space-3);
  flex-shrink: 0;
}

.avatar img {
  width: var(--avatar-size-lg);
  height: var(--avatar-size-lg);
  border-radius: var(--radius-xl);
  object-fit: cover;
  box-shadow: var(--shadow-sm);
}

.avatar-initials {
  width: var(--avatar-size-lg);
  height: var(--avatar-size-lg);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  text-transform: uppercase;
  box-shadow: var(--shadow-sm);
}

.online-indicator {
  position: absolute;
  bottom: var(--space-1);
  right: var(--space-1);
  width: var(--space-3);
  height: var(--space-3);
  background: var(--color-success);
  border: 2px solid var(--color-background-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-xs);
}

.contact-info {
  flex: 1;
  min-width: 0;
}

.contact-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: var(--letter-spacing-tight);
}

.contact-status {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: var(--line-height-tight);
}

.contact-actions {
  display: flex;
  gap: var(--space-2);
  margin-left: var(--space-3);
}

/* 图标样式 */
.action-btn svg {
  width: 20px;
  height: 20px;
  transition: all 0.2s ease-in-out;
}

.action-btn:hover svg {
  width: 22px;
  height: 22px;
}

.action-btn.small svg {
  width: 16px;
  height: 16px;
}

.action-btn.small:hover svg {
  width: 18px;
  height: 18px;
}

.search-icon {
  width: 20px;
  height: 20px;
}

.empty-icon svg {
  width: 64px;
  height: 64px;
}
</style>