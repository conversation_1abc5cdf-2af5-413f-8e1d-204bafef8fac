/* Chateo Design Tokens */
/* 基于 Figma 设计稿提取的设计令牌 */

:root {
  /* ===== 颜色系统 ===== */
  
  /* 主品牌色 */
  --color-primary-50: #EBF8FF;
  --color-primary-100: #BEE3F8;
  --color-primary-200: #90CDF4;
  --color-primary-300: #63B3ED;
  --color-primary-400: #4299E1;
  --color-primary-500: #007AFF; /* 主色 */
  --color-primary-600: #0066CC;
  --color-primary-700: #0051D5;
  --color-primary-800: #003D99;
  --color-primary-900: #002966;
  
  /* 辅助色 */
  --color-success: #34C759;
  --color-success-light: #4CD964;
  --color-success-dark: #28A745;
  
  --color-warning: #FF9500;
  --color-warning-light: #FFAD33;
  --color-warning-dark: #CC7700;
  
  --color-error: #FF3B30;
  --color-error-light: #FF5C5C;
  --color-error-dark: #CC2E26;
  
  --color-info: #5AC8FA;
  --color-info-light: #7DD3FC;
  --color-info-dark: #0EA5E9;
  
  /* 中性色 */
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;
  
  /* 语义化背景色 */
  --color-background-primary: #FFFFFF;
  --color-background-secondary: #F9FAFB;
  --color-background-tertiary: #F3F4F6;
  --color-background-overlay: rgba(0, 0, 0, 0.5);
  
  /* 语义化文本色 */
  --color-text-primary: #111827;
  --color-text-secondary: #6B7280;
  --color-text-tertiary: #9CA3AF;
  --color-text-inverse: #FFFFFF;
  --color-text-link: #007AFF;
  
  /* 边框色 */
  --color-border-primary: #E5E7EB;
  --color-border-secondary: #D1D5DB;
  --color-border-focus: #007AFF;
  --color-border-error: #FF3B30;
  
  /* ===== 字体系统 ===== */
  
  /* 字体族 */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  
  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 字母间距 */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  
  /* ===== 间距系统 ===== */
  
  --space-0: 0;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-7: 28px;
  --space-8: 32px;
  --space-9: 36px;
  --space-10: 40px;
  --space-11: 44px;
  --space-12: 48px;
  --space-14: 56px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;
  --space-28: 112px;
  --space-32: 128px;
  
  /* ===== 圆角系统 ===== */
  
  --radius-none: 0;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-3xl: 32px;
  --radius-full: 9999px;
  
  /* ===== 阴影系统 ===== */
  
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* ===== 组件尺寸 ===== */
  
  /* 头像尺寸 */
  --avatar-size-xs: 24px;
  --avatar-size-sm: 32px;
  --avatar-size-md: 40px;
  --avatar-size-lg: 56px;
  --avatar-size-xl: 80px;
  --avatar-size-2xl: 96px;
  
  /* 按钮高度 */
  --button-height-sm: 32px;
  --button-height-md: 40px;
  --button-height-lg: 48px;
  
  /* 输入框高度 */
  --input-height-sm: 36px;
  --input-height-md: 44px;
  --input-height-lg: 52px;
  
  /* 图标尺寸 */
  --icon-size-xs: 16px;
  --icon-size-sm: 20px;
  --icon-size-md: 24px;
  --icon-size-lg: 32px;
  --icon-size-xl: 40px;
  
  /* ===== 动画系统 ===== */
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  
  /* 动画缓动 */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* ===== 断点系统 ===== */
  
  --breakpoint-xs: 320px;
  --breakpoint-sm: 375px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* ===== Z-index 层级 ===== */
  
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

/* ===== 深色模式 ===== */
[data-theme="dark"] {
  /* 背景色 */
  --color-background-primary: #000000;
  --color-background-secondary: #1C1C1E;
  --color-background-tertiary: #2C2C2E;
  --color-background-overlay: rgba(0, 0, 0, 0.8);
  
  /* 文本色 */
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #EBEBF5;
  --color-text-tertiary: #EBEBF599;
  --color-text-inverse: #000000;
  
  /* 边框色 */
  --color-border-primary: #38383A;
  --color-border-secondary: #48484A;
  
  /* 中性色调整 */
  --color-gray-50: #1C1C1E;
  --color-gray-100: #2C2C2E;
  --color-gray-200: #38383A;
  --color-gray-300: #48484A;
  --color-gray-400: #636366;
  --color-gray-500: #8E8E93;
  --color-gray-600: #AEAEB2;
  --color-gray-700: #C7C7CC;
  --color-gray-800: #D1D1D6;
  --color-gray-900: #F2F2F7;
}

/* ===== 工具类 ===== */

/* 文本样式 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* 颜色工具类 */
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-inverse { color: var(--color-text-inverse); }
.text-link { color: var(--color-text-link); }

.bg-primary { background-color: var(--color-background-primary); }
.bg-secondary { background-color: var(--color-background-secondary); }
.bg-tertiary { background-color: var(--color-background-tertiary); }

/* 间距工具类 */
.p-0 { padding: var(--space-0); }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.m-0 { margin: var(--space-0); }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

/* 圆角工具类 */
.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 阴影工具类 */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }
.shadow-none { box-shadow: none; }

/* 动画工具类 */
.transition-fast { transition-duration: var(--duration-fast); }
.transition-normal { transition-duration: var(--duration-normal); }
.transition-slow { transition-duration: var(--duration-slow); }

.ease-linear { transition-timing-function: var(--ease-linear); }
.ease-in { transition-timing-function: var(--ease-in); }
.ease-out { transition-timing-function: var(--ease-out); }
.ease-in-out { transition-timing-function: var(--ease-in-out); }
.ease-bounce { transition-timing-function: var(--ease-bounce); }

/* 响应式工具类 */
@media (min-width: 375px) {
  .sm\:text-sm { font-size: var(--font-size-sm); }
  .sm\:text-base { font-size: var(--font-size-base); }
  .sm\:p-4 { padding: var(--space-4); }
  .sm\:m-4 { margin: var(--space-4); }
}

@media (min-width: 768px) {
  .md\:text-lg { font-size: var(--font-size-lg); }
  .md\:text-xl { font-size: var(--font-size-xl); }
  .md\:p-6 { padding: var(--space-6); }
  .md\:m-6 { margin: var(--space-6); }
}

@media (min-width: 1024px) {
  .lg\:text-xl { font-size: var(--font-size-xl); }
  .lg\:text-2xl { font-size: var(--font-size-2xl); }
  .lg\:p-8 { padding: var(--space-8); }
  .lg\:m-8 { margin: var(--space-8); }
}

/* ===== 组件基础样式 ===== */

/* 重置样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 触摸优化 */
.touchable {
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  transition: all var(--duration-fast) var(--ease-out);
}

.touchable:hover {
  transform: scale(1.02);
}

.touchable:active {
  transform: scale(0.98);
}

/* 焦点样式 */
.focusable:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.focusable:focus:not(:focus-visible) {
  outline: none;
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-100) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* 选择文本样式 */
::selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

/* 占位符样式 */
::placeholder {
  color: var(--color-text-tertiary);
  opacity: 1;
}

/* 禁用状态 */
.disabled,
[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 隐藏元素 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 截断文本 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}