import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从localStorage获取token，避免在拦截器中使用store
    const userStore = JSON.parse(localStorage.getItem('user-store') || '{}')
    const accessToken = userStore.accessToken

    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  (error) => {
    // 处理401未授权错误
    if (error.response?.status === 401) {
      // 清除本地存储的用户信息
      localStorage.removeItem('user-store')

      // 如果当前不在登录页面，跳转到登录页面
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    }

    // 处理其他错误
    // 支持多种错误响应格式
    const message =
      error.response?.data?.error?.message ||  // 新的错误格式 {error: {message: "..."}}
      error.response?.data?.message ||         // 旧的格式 {message: "..."}
      error.response?.data?.detail ||          // FastAPI默认格式 {detail: "..."}
      error.message ||                         // 网络错误等
      '请求失败'

    console.error('API Error:', message)

    return Promise.reject({
      status: error.response?.status,
      message,
      errorCode: error.response?.data?.error?.code || error.response?.data?.error_code
    })
  }
)

// API接口定义
export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email?: string
  nickname?: string
  password: string
}

export interface User {
  id: number
  username: string
  email?: string
  nickname: string
  avatar_url?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface AuthResponse {
  message: string
  user: User
  access_token: string
  refresh_token: string
  token_type: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
}

// 认证相关API
export const authAPI = {
  // 登录
  login: (data: LoginRequest): Promise<AuthResponse> => {
    return api.post('/auth/login', data)
  },

  // 注册
  register: (data: RegisterRequest): Promise<AuthResponse> => {
    return api.post('/auth/register', data)
  },

  // 获取当前用户信息
  getCurrentUser: (): Promise<User> => {
    return api.get('/auth/me')
  },

  // 刷新token
  refreshToken: (refreshToken: string): Promise<{ access_token: string; token_type: string }> => {
    return api.post('/auth/refresh', { refresh_token: refreshToken })
  },

  // 登出
  logout: (): Promise<{ message: string }> => {
    return api.post('/auth/logout')
  }
}

// 聊天相关API
export const chatAPI = {
  // 获取聊天列表
  getChats: (): Promise<ApiResponse<any[]>> => {
    return api.get('/chats')
  },
  
  // 获取聊天消息
  getMessages: (chatId: string, page = 1, limit = 50): Promise<ApiResponse<any[]>> => {
    return api.get(`/chats/${chatId}/messages`, {
      params: { page, limit }
    })
  },
  
  // 发送消息
  sendMessage: (chatId: string, data: { content: string; type: string }): Promise<ApiResponse<any>> => {
    return api.post(`/chats/${chatId}/messages`, data)
  },
  
  // 创建私聊
  createPrivateChat: (userId: string): Promise<ApiResponse<any>> => {
    return api.post('/chats/private', { userId })
  },
  
  // 创建群聊
  createGroupChat: (data: { name: string; participants: string[] }): Promise<ApiResponse<any>> => {
    return api.post('/chats/group', data)
  },
  
  // 标记消息为已读
  markAsRead: (chatId: string): Promise<ApiResponse> => {
    return api.put(`/chats/${chatId}/read`)
  }
}

// 联系人相关API
export const contactAPI = {
  // 获取联系人列表
  getContacts: (): Promise<ApiResponse<any[]>> => {
    return api.get('/contacts')
  },
  
  // 搜索用户
  searchUsers: (keyword: string): Promise<ApiResponse<any[]>> => {
    return api.get('/users/search', {
      params: { keyword }
    })
  },
  
  // 发送好友请求
  sendFriendRequest: (data: { userId: string; message?: string }): Promise<ApiResponse> => {
    return api.post('/contacts/requests', data)
  },
  
  // 获取好友请求列表
  getFriendRequests: (): Promise<ApiResponse<any[]>> => {
    return api.get('/contacts/requests')
  },
  
  // 处理好友请求
  handleFriendRequest: (requestId: string, action: 'accept' | 'reject'): Promise<ApiResponse> => {
    return api.put(`/contacts/requests/${requestId}`, { action })
  },
  
  // 删除联系人
  removeContact: (contactId: string): Promise<ApiResponse> => {
    return api.delete(`/contacts/${contactId}`)
  },
  
  // 拉黑/取消拉黑联系人
  toggleBlockContact: (contactId: string): Promise<ApiResponse> => {
    return api.put(`/contacts/${contactId}/block`)
  },
  
  // 更新联系人备注
  updateContactRemark: (contactId: string, remark: string): Promise<ApiResponse> => {
    return api.put(`/contacts/${contactId}/remark`, { remark })
  }
}

// 用户相关API
export const userAPI = {
  // 获取用户信息
  getUserInfo: (userId: string): Promise<ApiResponse<any>> => {
    return api.get(`/users/${userId}`)
  },
  
  // 更新用户信息
  updateProfile: (data: any): Promise<ApiResponse<any>> => {
    return api.put('/users/profile', data)
  },
  
  // 上传头像
  uploadAvatar: (file: File): Promise<ApiResponse<{ url: string }>> => {
    const formData = new FormData()
    formData.append('avatar', file)
    
    return api.post('/users/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 文件上传API
export const fileAPI = {
  // 上传文件
  upload: (file: File, type: 'image' | 'file' | 'audio' = 'file'): Promise<ApiResponse<{ url: string; filename: string }>> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)
    
    return api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export default api