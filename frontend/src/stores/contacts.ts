import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from './user'

export interface Contact extends User {
  isBlocked: boolean
  isFriend: boolean
  addedAt: Date
  remark?: string
}

export interface ContactRequest {
  id: string
  fromUserId: string
  toUserId: string
  message: string
  status: 'pending' | 'accepted' | 'rejected'
  createdAt: Date
}

export const useContactsStore = defineStore('contacts', () => {
  const contacts = ref<Contact[]>([])
  const contactRequests = ref<ContactRequest[]>([])
  const searchResults = ref<User[]>([])

  // 好友列表
  const friends = computed(() => {
    return contacts.value.filter(contact => contact.isFriend && !contact.isBlocked)
  })

  // 黑名单
  const blockedContacts = computed(() => {
    return contacts.value.filter(contact => contact.isBlocked)
  })

  // 待处理的好友请求
  const pendingRequests = computed(() => {
    return contactRequests.value.filter(request => request.status === 'pending')
  })

  // 获取联系人列表
  const fetchContacts = async () => {
    try {
      // TODO: 调用API获取联系人
      console.log('获取联系人列表')
      
      // 模拟数据
      contacts.value = [
        {
          id: '2',
          username: 'zhangsan',
          nickname: '张三',
          status: 'online',
          isBlocked: false,
          isFriend: true,
          addedAt: new Date(Date.now() - 86400000 * 7),
          remark: '同事'
        },
        {
          id: '3',
          username: 'lisi',
          nickname: '李四',
          status: 'offline',
          isBlocked: false,
          isFriend: true,
          addedAt: new Date(Date.now() - 86400000 * 30),
          remark: '朋友'
        },
        {
          id: '4',
          username: 'wangwu',
          nickname: '王五',
          status: 'away',
          isBlocked: false,
          isFriend: true,
          addedAt: new Date(Date.now() - 86400000 * 60)
        }
      ]
    } catch (error) {
      console.error('获取联系人失败:', error)
    }
  }

  // 搜索用户
  const searchUsers = async (keyword: string) => {
    try {
      if (!keyword.trim()) {
        searchResults.value = []
        return
      }
      
      // TODO: 调用API搜索用户
      console.log('搜索用户:', keyword)
      
      // 模拟搜索结果
      searchResults.value = [
        {
          id: '5',
          username: 'test' + keyword,
          nickname: '测试用户' + keyword,
          status: 'online'
        }
      ]
    } catch (error) {
      console.error('搜索用户失败:', error)
      searchResults.value = []
    }
  }

  // 发送好友请求
  const sendFriendRequest = async (userId: string, message: string = '') => {
    try {
      // TODO: 调用API发送好友请求
      console.log('发送好友请求:', { userId, message })
      
      const request: ContactRequest = {
        id: Date.now().toString(),
        fromUserId: '1', // 当前用户ID
        toUserId: userId,
        message,
        status: 'pending',
        createdAt: new Date()
      }
      
      contactRequests.value.push(request)
      return { success: true }
    } catch (error) {
      console.error('发送好友请求失败:', error)
      return { success: false, error: '发送失败' }
    }
  }

  // 处理好友请求
  const handleFriendRequest = async (requestId: string, action: 'accept' | 'reject') => {
    try {
      // TODO: 调用API处理好友请求
      console.log('处理好友请求:', { requestId, action })
      
      const request = contactRequests.value.find(r => r.id === requestId)
      if (!request) return { success: false, error: '请求不存在' }
      
      request.status = action === 'accept' ? 'accepted' : 'rejected'
      
      // 如果接受，添加到联系人列表
      if (action === 'accept') {
        // 这里需要获取用户信息，暂时模拟
        const newContact: Contact = {
          id: request.fromUserId,
          username: 'user' + request.fromUserId,
          nickname: '新朋友',
          status: 'online',
          isBlocked: false,
          isFriend: true,
          addedAt: new Date()
        }
        contacts.value.push(newContact)
      }
      
      return { success: true }
    } catch (error) {
      console.error('处理好友请求失败:', error)
      return { success: false, error: '处理失败' }
    }
  }

  // 删除联系人
  const removeContact = async (contactId: string) => {
    try {
      // TODO: 调用API删除联系人
      console.log('删除联系人:', contactId)
      
      const index = contacts.value.findIndex(c => c.id === contactId)
      if (index > -1) {
        contacts.value.splice(index, 1)
      }
      
      return { success: true }
    } catch (error) {
      console.error('删除联系人失败:', error)
      return { success: false, error: '删除失败' }
    }
  }

  // 拉黑/取消拉黑联系人
  const toggleBlockContact = async (contactId: string) => {
    try {
      // TODO: 调用API拉黑/取消拉黑
      console.log('切换拉黑状态:', contactId)
      
      const contact = contacts.value.find(c => c.id === contactId)
      if (contact) {
        contact.isBlocked = !contact.isBlocked
      }
      
      return { success: true }
    } catch (error) {
      console.error('操作失败:', error)
      return { success: false, error: '操作失败' }
    }
  }

  // 更新联系人备注
  const updateContactRemark = async (contactId: string, remark: string) => {
    try {
      // TODO: 调用API更新备注
      console.log('更新联系人备注:', { contactId, remark })
      
      const contact = contacts.value.find(c => c.id === contactId)
      if (contact) {
        contact.remark = remark
      }
      
      return { success: true }
    } catch (error) {
      console.error('更新备注失败:', error)
      return { success: false, error: '更新失败' }
    }
  }

  return {
    contacts,
    contactRequests,
    searchResults,
    friends,
    blockedContacts,
    pendingRequests,
    fetchContacts,
    searchUsers,
    sendFriendRequest,
    handleFriendRequest,
    removeContact,
    toggleBlockContact,
    updateContactRemark
  }
})