<template>
  <Teleport to="body">
    <div class="toast-container-wrapper">
      <TransitionGroup
        name="toast-list"
        tag="div"
        class="toast-list"
      >
        <div
          v-for="toast in toasts"
          :key="toast.id"
          class="toast-item"
        >
          <div
            class="toast-content"
            :class="`toast-${toast.type}`"
            @click="handleToastClick(toast)"
          >
            <div class="toast-inner">
              <div class="toast-icon">
                <CheckCircle v-if="toast.type === 'success'" />
                <XCircle v-else-if="toast.type === 'error'" />
                <AlertTriangle v-else-if="toast.type === 'warning'" />
                <Info v-else-if="toast.type === 'info'" />
              </div>
              <div class="toast-message">{{ toast.message }}</div>
              <button
                v-if="toast.closable"
                class="toast-close"
                @click.stop="closeToast(toast.id)"
              >
                <X :size="16" />
              </button>
            </div>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { CheckCircle, XCircle, AlertTriangle, Info, X } from 'lucide-vue-next'

export interface ToastItem {
  id: string
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration: number
  closable: boolean
  onClick?: () => void
  onClose?: () => void
}

const toasts = ref<ToastItem[]>([])
const timers = new Map<string, number>()
const maxToasts = 5

const addToast = (toast: Omit<ToastItem, 'id'>) => {
  const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
  const newToast: ToastItem = { ...toast, id }
  
  // 如果超过最大数量，移除最旧的Toast
  if (toasts.value.length >= maxToasts) {
    const oldestToast = toasts.value[0]
    closeToast(oldestToast.id)
  }
  
  toasts.value.push(newToast)
  
  // 设置自动关闭定时器
  if (newToast.duration > 0) {
    const timer = setTimeout(() => {
      closeToast(id)
    }, newToast.duration)
    timers.set(id, timer)
  }
  
  return id
}

const closeToast = (id: string) => {
  const index = toasts.value.findIndex(t => t.id === id)
  if (index > -1) {
    const toast = toasts.value[index]
    
    // 清除定时器
    const timer = timers.get(id)
    if (timer) {
      clearTimeout(timer)
      timers.delete(id)
    }
    
    // 移除Toast
    toasts.value.splice(index, 1)
    
    // 调用关闭回调
    toast.onClose?.()
  }
}

const clearAll = () => {
  // 清除所有定时器
  timers.forEach(timer => clearTimeout(timer))
  timers.clear()
  
  // 调用所有Toast的关闭回调
  toasts.value.forEach(toast => toast.onClose?.())
  
  // 清空Toast列表
  toasts.value = []
}

const handleToastClick = (toast: ToastItem) => {
  toast.onClick?.()
}

defineExpose({
  addToast,
  closeToast,
  clearAll
})
</script>

<style scoped>
.toast-container-wrapper {
  position: fixed;
  top: var(--space-5, 20px);
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  pointer-events: none;
}

.toast-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  align-items: center;
}

.toast-item {
  pointer-events: auto;
}

.toast-content {
  max-width: calc(80vw - var(--space-4));
  min-width: 420px;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  cursor: pointer;
  user-select: none;
}

.toast-inner {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius-lg);
  border: 1px solid;
  font-family: var(--font-family-primary);
}

.toast-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-message {
  flex: 1;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  word-break: break-word;
}

.toast-close {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--space-6);
  height: var(--space-6);
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.6;
}

.toast-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

/* Toast类型样式 */
.toast-success {
  background: var(--color-success);
}

.toast-success .toast-inner {
  border-color: var(--color-success-light);
  color: var(--color-text-inverse);
}

.toast-error {
  background: var(--color-error);
}

.toast-error .toast-inner {
  border-color: var(--color-error-light);
  color: var(--color-text-inverse);
}

.toast-warning {
  background: var(--color-warning);
}

.toast-warning .toast-inner {
  border-color: var(--color-warning-light);
  color: var(--color-text-inverse);
}

.toast-info {
  background: var(--color-info);
}

.toast-info .toast-inner {
  border-color: var(--color-info-light);
  color: var(--color-text-inverse);
}

/* 列表动画 - 增强版 */
.toast-list-enter-active {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toast-list-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.toast-list-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
  filter: blur(4px);
}

.toast-list-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
  filter: blur(4px);
}

.toast-list-move {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 悬停动画效果 */
.toast-content {
  transition: all 0.2s ease-in-out;
}

.toast-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 点击动画效果 */
.toast-content:active {
  transform: translateY(0) scale(0.98);
  transition: transform 0.1s ease-in-out;
}

/* 关闭按钮动画 */
.toast-close {
  transition: all 0.2s ease-in-out;
}

.toast-close:hover {
  transform: scale(1.1) rotate(90deg);
  background-color: rgba(255, 255, 255, 0.2);
}

.toast-close:active {
  transform: scale(0.95) rotate(90deg);
}

/* 图标样式和动画 */
.toast-icon {
  transition: transform 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-icon svg {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease-in-out;
}

.toast-content:hover .toast-icon {
  transform: scale(1.1) rotate(5deg);
}

.toast-content:hover .toast-icon svg {
  width: 24px;
  height: 24px;
}

/* 进入时的弹性动画 */
@keyframes toast-bounce-in {
  0% {
    opacity: 0;
    transform: translateX(100%) scale(0.3) rotate(10deg);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-10%) scale(1.05) rotate(-2deg);
  }
  70% {
    transform: translateX(5%) scale(0.98) rotate(1deg);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1) rotate(0deg);
  }
}

/* 离开时的滑出动画 */
@keyframes toast-slide-out {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
}

/* 应用动画 */
.toast-list-enter-active .toast-content {
  animation: toast-bounce-in 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toast-list-leave-active .toast-content {
  animation: toast-slide-out 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .toast-success {
    background: var(--color-success-dark);
  }
  
  .toast-error {
    background: var(--color-error-dark);
  }
  
  .toast-warning {
    background: var(--color-warning-dark);
  }
  
  .toast-info {
    background: var(--color-info-dark);
  }
}

/* 响应式适配 */
@media (max-width: 480px) {
  .toast-container-wrapper {
    top: calc(var(--space-5, 20px) + var(--space-2));
  }
  
  .toast-content {
    max-width: calc(100vw - var(--space-6));
    min-width: auto;
  }
  
  .toast-inner {
    padding: var(--space-3) var(--space-4);
    gap: var(--space-2);
  }
  
  .toast-message {
    font-size: var(--font-size-xs);
  }
}
</style>